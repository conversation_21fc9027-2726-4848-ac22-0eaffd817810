import type { Express, Request, Response, NextFunction } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import { setupAuth, isAuthenticated } from "./supabaseAuth";
import {
  topics, insertTopicSchema,
  chapters, insertChapterSchema,
  exercises, insertExerciseSchema,
  userProgress, insertUserProgressSchema,
  upsertUserSchema, studentAnswers, insertStudentAnswerSchema
} from "@shared/schema";
import { ZodError } from "zod";
import { generateTopicDescription, generateChapterContent, generateExercise } from "./lib/openai";

// Helper function to handle errors
const handleError = (res: Response, error: any) => {
  console.error("API Error:", error);

  if (error instanceof ZodError) {
    return res.status(400).json({
      error: "Validation error",
      details: error.errors
    });
  }

  const message = error.message || "Internal server error";
  const status = error.status || 500;
  res.status(status).json({
    error: message
  });
};

export async function registerRoutes(app: Express): Promise<Server> {
  // Auth setup
  await setupAuth(app);

  // Auth route to get current user
  app.get('/api/auth/user', isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.id;
      const user = await storage.getUser(userId);
      res.json(user);
    } catch (error) {
      handleError(res, error);
    }
  });

  // Topic routes
  app.get("/api/topics", async (req, res) => {
    try {
      // For public access, only return published topics
      const topics = await storage.getPublishedTopics();
      res.json(topics);
    } catch (error) {
      handleError(res, error);
    }
  });

  app.get("/api/topics/category/:category", async (req, res) => {
    try {
      const { category } = req.params;
      // For public access, only return published topics
      const topics = await storage.getPublishedTopicsByCategory(category);
      res.json(topics);
    } catch (error) {
      handleError(res, error);
    }
  });

  app.get("/api/topics/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const topic = await storage.getTopic(id);

      if (!topic) {
        return res.status(404).json({ error: "Topic not found" });
      }

      res.json(topic);
    } catch (error) {
      handleError(res, error);
    }
  });

  app.post("/api/topics", isAuthenticated, async (req, res) => {
    try {
      const topicData = insertTopicSchema.parse(req.body);

      // Check if a topic with the same name already exists
      const existingTopics = await storage.getAllTopics();
      const duplicateTopic = existingTopics.find(topic =>
        topic.name.toLowerCase() === topicData.name.toLowerCase()
      );

      if (duplicateTopic) {
        return res.status(409).json({
          error: `A topic named "${topicData.name}" already exists. Please choose a different name.`
        });
      }

      // If no description provided, generate one using OpenAI
      if (!topicData.description && topicData.name && topicData.category) {
        try {
          const description = await generateTopicDescription(
            topicData.category,
            topicData.name
          );
          topicData.description = description;
        } catch (genError) {
          console.error("Error generating description:", genError);
          // Continue without generated description
        }
      }

      const topic = await storage.createTopic(topicData);
      res.status(201).json(topic);
    } catch (error) {
      handleError(res, error);
    }
  });

  // Admin routes for topics (returns all topics including unpublished)
  app.get("/api/admin/topics", isAuthenticated, async (req, res) => {
    try {
      const topics = await storage.getAllTopics();
      res.json(topics);
    } catch (error) {
      handleError(res, error);
    }
  });

  // Update topic
  app.patch("/api/topics/:id", isAuthenticated, async (req, res) => {
    try {
      const topicId = parseInt(req.params.id);
      const { published } = req.body;

      if (typeof published !== 'boolean') {
        return res.status(400).json({ error: "Published status must be a boolean" });
      }

      const updatedTopic = await storage.updateTopic(topicId, { published });
      res.json(updatedTopic);
    } catch (error) {
      handleError(res, error);
    }
  });

  // Chapter routes
  app.get("/api/topics/:topicId/chapters", async (req, res) => {
    try {
      const topicId = parseInt(req.params.topicId);
      // For public access, only return published chapters
      const chapters = await storage.getPublishedChaptersByTopic(topicId);
      res.json(chapters);
    } catch (error) {
      handleError(res, error);
    }
  });

  app.get("/api/chapters/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      // For public access, only return published chapters
      const chapter = await storage.getPublishedChapter(id);

      if (!chapter) {
        return res.status(404).json({ error: "Chapter not found or not published" });
      }

      res.json(chapter);
    } catch (error) {
      handleError(res, error);
    }
  });

  app.post("/api/chapters", isAuthenticated, async (req, res) => {
    try {
      const chapterData = insertChapterSchema.parse(req.body);
      const chapter = await storage.createChapter(chapterData);
      res.status(201).json(chapter);
    } catch (error) {
      handleError(res, error);
    }
  });

  // Admin routes for chapters (returns all chapters including unpublished)
  app.get("/api/admin/topics/:topicId/chapters", isAuthenticated, async (req, res) => {
    try {
      const topicId = parseInt(req.params.topicId);
      const chapters = await storage.getChaptersByTopic(topicId);
      res.json(chapters);
    } catch (error) {
      handleError(res, error);
    }
  });

  // Update chapter
  app.patch("/api/chapters/:id", isAuthenticated, async (req, res) => {
    try {
      const chapterId = parseInt(req.params.id);
      const { published } = req.body;

      if (typeof published !== 'boolean') {
        return res.status(400).json({ error: "Published status must be a boolean" });
      }

      const updatedChapter = await storage.updateChapter(chapterId, { published });
      res.json(updatedChapter);
    } catch (error) {
      handleError(res, error);
    }
  });

  // Get all chapters endpoint
  app.get("/api/chapters", async (req, res) => {
    try {
      const topics = await storage.getAllTopics();
      const allChapters = [];

      for (const topic of topics) {
        const chapters = await storage.getChaptersByTopic(topic.id);
        allChapters.push(...chapters);
      }

      res.json(allChapters);
    } catch (error) {
      handleError(res, error);
    }
  });

  // Exercise routes
  app.get("/api/chapters/:chapterId/exercises", async (req, res) => {
    try {
      const chapterId = parseInt(req.params.chapterId);
      const exercises = await storage.getExercisesByChapter(chapterId);
      res.json(exercises);
    } catch (error) {
      handleError(res, error);
    }
  });

  // Admin route for exercises (returns all exercises including unpublished)
  app.get("/api/admin/chapters/:chapterId/exercises", isAuthenticated, async (req, res) => {
    try {
      const chapterId = parseInt(req.params.chapterId);
      // For admin, we need a method that returns all exercises regardless of published status
      const exercises = await storage.getAllExercisesByChapter(chapterId);
      res.json(exercises);
    } catch (error) {
      handleError(res, error);
    }
  });

  app.get("/api/exercises/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const exercise = await storage.getExercise(id);

      if (!exercise) {
        return res.status(404).json({ error: "Exercise not found" });
      }

      res.json(exercise);
    } catch (error) {
      handleError(res, error);
    }
  });

  app.post("/api/exercises", isAuthenticated, async (req, res) => {
    try {
      const exerciseData = insertExerciseSchema.parse(req.body);
      const exercise = await storage.createExercise(exerciseData);
      res.status(201).json(exercise);
    } catch (error) {
      handleError(res, error);
    }
  });

  // Update exercise
  app.patch("/api/exercises/:id", isAuthenticated, async (req, res) => {
    try {
      const exerciseId = parseInt(req.params.id);
      const { published } = req.body;

      if (typeof published !== 'boolean') {
        return res.status(400).json({ error: "Published status must be a boolean" });
      }

      const updatedExercise = await storage.updateExercise(exerciseId, { published });
      res.json(updatedExercise);
    } catch (error) {
      handleError(res, error);
    }
  });

  // User progress routes
  app.get("/api/user/progress", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.id;
      const progress = await storage.getUserProgress(userId);
      res.json(progress);
    } catch (error) {
      handleError(res, error);
    }
  });

  app.get("/api/user/progress/:chapterId", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.id;
      const chapterId = parseInt(req.params.chapterId);
      const progress = await storage.getChapterProgress(userId, chapterId);

      if (!progress) {
        return res.json({ completed: false, score: 0 });
      }

      res.json(progress);
    } catch (error) {
      handleError(res, error);
    }
  });

  app.post("/api/user/progress", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.id;
      const progressData = insertUserProgressSchema.parse({
        ...req.body,
        userId
      });

      const progress = await storage.createOrUpdateUserProgress(progressData);
      res.status(201).json(progress);
    } catch (error) {
      handleError(res, error);
    }
  });

  // Achievement routes
  app.get("/api/achievements", async (req, res) => {
    try {
      const achievements = await storage.getAllAchievements();
      res.json(achievements);
    } catch (error) {
      handleError(res, error);
    }
  });

  app.get("/api/user/achievements", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.id;
      const achievements = await storage.getUserAchievements(userId);
      res.json(achievements);
    } catch (error) {
      handleError(res, error);
    }
  });

  // Student answers routes
  app.get("/api/student-answers/:exerciseId", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.id;
      const exerciseId = parseInt(req.params.exerciseId);
      const answers = await storage.getStudentAnswers(userId, exerciseId);
      res.json(answers);
    } catch (error) {
      handleError(res, error);
    }
  });

  app.post("/api/student-answers", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.id;
      const { exerciseId, answer } = req.body;

      if (!exerciseId || answer === undefined) {
        return res.status(400).json({ error: "Exercise ID and answer are required" });
      }

      // Get the exercise to validate the answer
      const exercise = await storage.getExercise(exerciseId);
      if (!exercise) {
        return res.status(404).json({ error: "Exercise not found" });
      }

      // Validate the answer based on exercise type and content
      let isCorrect = false;
      const content = exercise.content as any;

      if (exercise.type === 'multiple-choice') {
        isCorrect = answer === content.correctAnswer;
      } else if (exercise.type === 'fill-in-blank') {
        const correctAnswer = content.answer || content.correctAnswer;
        isCorrect = String(answer).toLowerCase().trim() === String(correctAnswer).toLowerCase().trim();
      } else if (exercise.type === 'matching') {
        // For matching, answer should be an object with all correct pairs
        if (typeof answer === 'object' && content.pairs) {
          isCorrect = content.pairs.every((pair: any) =>
            answer[pair.term] === pair.definition
          );
        }
      } else {
        // Arithmetic and other types
        isCorrect = answer === content.correctAnswer;
      }

      // Check if there's already an answer for this user and exercise
      const existingAnswers = await storage.getStudentAnswers(userId, exerciseId);

      let studentAnswer;
      if (existingAnswers.length > 0) {
        // Update existing answer
        const existingAnswer = existingAnswers[0];
        studentAnswer = await storage.updateStudentAnswer(existingAnswer.id, {
          answer: answer,
          isCorrect,
          attemptsCount: existingAnswer.attemptsCount + 1
        });
      } else {
        // Create new answer
        const answerData = insertStudentAnswerSchema.parse({
          userId,
          exerciseId,
          answer: answer,
          isCorrect,
          attemptsCount: 1
        });
        studentAnswer = await storage.createStudentAnswer(answerData);
      }

      res.json({ isCorrect, studentAnswer });
    } catch (error) {
      handleError(res, error);
    }
  });

  // Generation routes
  app.post("/api/generate/chapter", isAuthenticated, async (req, res) => {
    try {
      const { topic, chapterTitle } = req.body;

      if (!topic || !chapterTitle) {
        return res.status(400).json({ error: "Topic and chapter title are required" });
      }

      const content = await generateChapterContent(topic, chapterTitle);
      res.json(content);
    } catch (error) {
      handleError(res, error);
    }
  });

  app.post("/api/generate/exercise", isAuthenticated, async (req, res) => {
    try {
      const { topic, difficulty, type } = req.body;

      if (!topic || !difficulty || !type) {
        return res.status(400).json({
          error: "Topic, difficulty, and type are required"
        });
      }

      const content = await generateExercise("General", topic, difficulty, 1);
      res.json(content);
    } catch (error) {
      handleError(res, error);
    }
  });

  // Generate content for a specific topic
  app.post("/api/generate/content", async (req, res) => {
    try {
      const {
        name,
        category,
        description,
        lessonCount,
        exercisesPerLesson,
        ageGroup,
        instructions
      } = req.body;

      // Validate required fields
      if (!name || !category) {
        return res.status(400).json({
          error: "Topic name and category are required"
        });
      }

      console.log(`Generating content for topic: ${name}`);

      // First, create the topic in the database
      let topicDescription = description;
      if (!topicDescription) {
        // Generate a description using OpenAI if one wasn't provided
        topicDescription = await generateTopicDescription(category, name) || `Learn about ${name} in a fun and interactive way.`;
      }

      const topic = await storage.createTopic({
        name,
        category,
        description: topicDescription,
        imageUrl: `https://via.placeholder.com/300x200/4F46E5/FFFFFF?text=${encodeURIComponent(name)}`
      });

      console.log(`Created topic: ${topic.name} (ID: ${topic.id})`);

      // Create each chapter and its exercises
      const createdChapters = [];

      // Create default chapter titles if none are provided
      const defaultChapterTitles = Array.from({ length: lessonCount }, (_, i) => {
        if (i === 0) return `Introduction to ${name}`;
        return `${name} Lesson ${i + 1}`;
      });

      for (let i = 0; i < lessonCount; i++) {
        const chapterTitle = defaultChapterTitles[i];
        console.log(`Generating chapter: ${chapterTitle}`);

        // Generate chapter content using OpenAI
        const chapterContent = await generateChapterContent(name, chapterTitle);

        // Create the chapter in the database
        const chapter = await storage.createChapter({
          topicId: topic.id,
          title: chapterTitle,
          description: chapterContent.description,
          order: i + 1,
          durationMinutes: chapterContent.durationMinutes,
          exerciseCount: exercisesPerLesson,
          pointsReward: chapterContent.pointsReward
        });

        console.log(`Created chapter: ${chapter.title} (ID: ${chapter.id})`);

        // Create exercises for this chapter
        const createdExercises = [];
        for (let j = 0; j < exercisesPerLesson; j++) {
          // Generate exercise content using OpenAI
          console.log(`Generating exercise ${j+1} for chapter: ${chapter.title}`);

          const difficulties = ["easy", "medium", "hard"];
          const difficulty = difficulties[Math.min(Math.floor(j / 2), difficulties.length - 1)];

          try {
            // Generate exercise using OpenAI
            const exerciseData = await generateExercise(category, chapter.title, difficulty, j + 1);

            // Create the exercise in the database
            const exercise = await storage.createExercise({
              chapterId: chapter.id,
              title: exerciseData.title,
              description: exerciseData.description,
              difficulty: difficulty,
              type: exerciseData.type,
              content: exerciseData.content,
              order: j + 1
            });

            createdExercises.push(exercise);
          } catch (error) {
            console.error(`Error generating exercise ${j+1}:`, error);

            // Create a fallback exercise if OpenAI fails
            const fallbackExercise = await storage.createExercise({
              chapterId: chapter.id,
              title: `Exercise ${j+1}`,
              description: `Practice your knowledge of ${name}.`,
              difficulty: difficulty,
              type: "multiple-choice",
              content: {
                question: `Question about ${name}`,
                options: ["Option A", "Option B", "Option C", "Option D"],
                correctAnswer: "Option B"
              },
              order: j + 1
            });

            createdExercises.push(fallbackExercise);
          }
        }

        // Add exercises to the chapter object
        const chapterWithExercises = {
          ...chapter,
          exercises: createdExercises
        };

        createdChapters.push(chapterWithExercises);
      }

      // Return the complete generated content
      const result = {
        topic,
        chapters: createdChapters
      };

      res.status(201).json(result);
      console.log(`Successfully generated content for topic: ${name}`);

    } catch (error) {
      console.error("Content generation failed:", error);
      handleError(res, error);
    }
  });

  // Initialization route
  app.post("/api/init", async (req, res) => {
    try {
      // Initialize default achievements
      await (storage as any).initializeDefaultAchievements();

      // Create default topics if none exist
      const existingTopics = await storage.getAllTopics();

      if (existingTopics.length === 0) {
        const categories = ["arithmetic", "geometry"];
        const topicsByCategory: Record<string, string[]> = {
          arithmetic: ["Addition", "Subtraction", "Multiplication", "Division"],
          geometry: ["Shapes", "Symmetry", "Patterns", "Measurements"]
        };

        for (const category of categories) {
          for (const name of topicsByCategory[category]) {
            await storage.createTopic({
              name,
              category,
              description: `Learn about ${name} with fun exercises!`,
              imageUrl: null
            });
          }
        }
      }

      res.json({ message: "Default data initialized" });
    } catch (error) {
      handleError(res, error);
    }
  });

  const httpServer = createServer(app);
  return httpServer;
}
