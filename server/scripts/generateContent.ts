import { db } from "../db";
import { storage } from "../storage";
import { generateChapterContent, generateExercise } from "../lib/openai";
import { topics, chapters, exercises } from "@shared/schema";
import { eq } from "drizzle-orm";

// This script will generate educational content for each topic in our database
async function generateTopicContent() {
  try {
    console.log("Starting content generation for math topics...");
    
    // Get all topics from the database
    const allTopics = await storage.getAllTopics();
    
    if (!allTopics.length) {
      console.log("No topics found in the database. Please run the initialization first.");
      return;
    }
    
    console.log(`Found ${allTopics.length} topics to process.`);
    
    // Define units/chapters for each topic based on the Khan Academy style
    const topicUnits = {
      // Arithmetic topics
      "Addition": [
        { title: "Counting", order: 1 },
        { title: "Addition and subtraction intro", order: 2 },
        { title: "Place value (tens and hundreds)", order: 3 },
        { title: "Addition and subtraction within 20", order: 4 },
        { title: "Addition and subtraction within 100", order: 5 },
        { title: "Addition and subtraction within 1000", order: 6 }
      ],
      "Subtraction": [
        { title: "Understanding subtraction", order: 1 },
        { title: "Subtraction with pictures", order: 2 },
        { title: "Subtraction within 10", order: 3 },
        { title: "Subtraction within 20", order: 4 },
        { title: "Subtraction with regrouping", order: 5 },
        { title: "Word problems with subtraction", order: 6 }
      ],
      "Multiplication": [
        { title: "Equal groups", order: 1 },
        { title: "Skip counting", order: 2 },
        { title: "Multiplication as repeated addition", order: 3 },
        { title: "Multiplication tables up to 5", order: 4 },
        { title: "Multiplication tables up to 10", order: 5 },
        { title: "Word problems with multiplication", order: 6 }
      ],
      "Division": [
        { title: "Introduction to division", order: 1 },
        { title: "Division as sharing", order: 2 },
        { title: "Division within 100", order: 3 },
        { title: "Division with remainders", order: 4 },
        { title: "Division word problems", order: 5 },
        { title: "Relationship between multiplication and division", order: 6 }
      ],
      
      // Geometry topics
      "Shapes": [
        { title: "2D shapes introduction", order: 1 },
        { title: "3D shapes introduction", order: 2 },
        { title: "Sides and vertices", order: 3 },
        { title: "Sorting and classifying shapes", order: 4 },
        { title: "Drawing shapes", order: 5 },
        { title: "Composite shapes", order: 6 }
      ],
      "Symmetry": [
        { title: "Identifying symmetry", order: 1 },
        { title: "Lines of symmetry", order: 2 },
        { title: "Symmetry in letters and numbers", order: 3 },
        { title: "Creating symmetrical designs", order: 4 },
        { title: "Rotational symmetry", order: 5 },
        { title: "Symmetry in our world", order: 6 }
      ],
      "Patterns": [
        { title: "Recognizing patterns", order: 1 },
        { title: "Shape patterns", order: 2 },
        { title: "Number patterns", order: 3 },
        { title: "Growing patterns", order: 4 },
        { title: "Pattern rules", order: 5 },
        { title: "Creating patterns", order: 6 }
      ],
      "Measurements": [
        { title: "Length, height, and width", order: 1 },
        { title: "Measuring with non-standard units", order: 2 },
        { title: "Measuring with standard units", order: 3 },
        { title: "Time concepts", order: 4 },
        { title: "Money concepts", order: 5 },
        { title: "Temperature and weight", order: 6 }
      ]
    };
    
    // Process each topic
    for (const topic of allTopics) {
      console.log(`Processing topic: ${topic.name} (${topic.category})`);
      
      // Get units for this topic
      const units = topicUnits[topic.name];
      if (!units) {
        console.log(`No units defined for topic ${topic.name}, skipping...`);
        continue;
      }
      
      // Check if chapters already exist for this topic
      const existingChapters = await db.select().from(chapters).where(eq(chapters.topicId, topic.id));
      
      if (existingChapters.length > 0) {
        console.log(`Topic ${topic.name} already has ${existingChapters.length} chapters, skipping...`);
        continue;
      }
      
      // Process each unit/chapter
      for (const unit of units) {
        console.log(`  - Generating chapter: ${unit.title}`);
        
        // Generate chapter content
        const chapterContent = await generateChapterContent(topic.name, unit.title);
        
        // Create chapter in database
        const chapter = await storage.createChapter({
          topicId: topic.id,
          title: unit.title,
          description: chapterContent.description,
          order: unit.order,
          durationMinutes: chapterContent.durationMinutes,
          exerciseCount: chapterContent.exerciseCount,
          pointsReward: chapterContent.pointsReward
        });
        
        console.log(`    Chapter created with ID: ${chapter.id}`);
        
        // Generate exercises for the chapter
        const exerciseTypes = ["multiple-choice", "fill-in-blank", "matching"];
        const difficultyLevels = ["easy", "medium", "hard"];
        
        for (let i = 0; i < chapterContent.exerciseCount; i++) {
          const exerciseType = exerciseTypes[i % exerciseTypes.length];
          const difficulty = difficultyLevels[Math.min(i, 2)];
          
          console.log(`    - Generating exercise ${i+1} (${difficulty}, ${exerciseType})`);
          
          // Generate exercise content
          const exerciseContent = await generateExercise(
            `${topic.name} - ${unit.title}`, 
            difficulty,
            exerciseType
          );
          
          // Create exercise in database
          const exercise = await storage.createExercise({
            chapterId: chapter.id,
            title: exerciseContent.title,
            description: exerciseContent.description,
            difficulty: difficulty,
            content: exerciseContent.content,
            type: exerciseType,
            order: i + 1
          });
          
          console.log(`      Exercise created with ID: ${exercise.id}`);
        }
        
        // Small delay to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }
    
    console.log("Content generation complete!");
  } catch (error) {
    console.error("Error generating content:", error);
  }
}

// Export the function so it can be run from another file
export { generateTopicContent };

// We're using ESM modules, so we don't need the require check
// Just export the function for use in other files