import { useState } from 'react';
import { useLocation } from 'wouter';
import { signInWithGoogle } from '@/lib/supabase';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Icons } from '@/components/ui/icons';

export default function SupabaseLogin() {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [, navigate] = useLocation();

  const handleGoogleSignIn = async () => {
    try {
      setIsLoading(true);
      setError(null);
      console.log('Starting Google sign-in process');
      
      const { error } = await signInWithGoogle();
      
      if (error) {
        throw new Error(error.message);
      }
      
      // The redirect happens automatically via Supabase
    } catch (err: any) {
      console.error('Login error:', err);
      setError(err.message || 'Failed to sign in with Google');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-4 bg-primary/5">
      <div className="w-full max-w-md">
        <Card className="w-full shadow-lg">
          <CardHeader className="space-y-1 text-center">
            <CardTitle className="text-3xl font-bold text-primary">Math Learning</CardTitle>
            <CardDescription className="text-lg">
              Explore fun, interactive math lessons
            </CardDescription>
          </CardHeader>
          
          <CardContent className="grid gap-6 p-6">
            <div className="flex flex-col space-y-4 text-center">
              <h3 className="font-medium text-xl">Sign in to get started</h3>
              <p className="text-sm text-muted-foreground">
                Continue with your Google account to track your progress and achievements
              </p>
            </div>
            
            <Button 
              onClick={handleGoogleSignIn} 
              disabled={isLoading}
              className="w-full h-11 bg-white hover:bg-slate-50 text-black border-gray-300 hover:border-gray-400"
              variant="outline"
              size="lg"
            >
              {isLoading ? (
                <span className="flex items-center justify-center gap-2">
                  <Icons.spinner className="h-4 w-4 mr-2" />
                  Connecting...
                </span>
              ) : (
                <span className="flex items-center justify-center gap-2">
                  <Icons.google className="h-4 w-4 mr-2" />
                  Sign in with Google
                </span>
              )}
            </Button>
            
            {error && (
              <div className="p-4 text-sm text-red-600 bg-red-50 rounded-md border border-red-200">
                <p className="font-medium">Sign-in failed</p>
                <p>{error}</p>
              </div>
            )}
          </CardContent>
          
          <CardFooter className="flex flex-col space-y-4 bg-slate-50 px-6 py-4 rounded-b-lg">
            <div className="text-sm text-center text-slate-600">
              <p>Perfect for students ages 5-12</p>
              <p className="mt-1 text-xs text-slate-500">
                By signing in, you agree to our Terms of Service and Privacy Policy.
              </p>
            </div>
          </CardFooter>
        </Card>
      </div>
    </div>
  );
}