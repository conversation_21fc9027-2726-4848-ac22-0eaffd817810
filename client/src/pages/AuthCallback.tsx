import { useEffect, useState } from 'react';
import { useLocation } from 'wouter';
import { supabase } from '@/lib/supabase';

export default function AuthCallback() {
  const [error, setError] = useState<string | null>(null);
  const [, navigate] = useLocation();

  useEffect(() => {
    // Process the OAuth callback
    const handleAuthCallback = async () => {
      try {
        // The hash contains the access token
        const { hash } = window.location;
        
        if (!hash) {
          return navigate('/login');
        }

        // Exchange the OAuth token for a session
        // Supabase will handle this automatically from the URL
        const { error: sessionError } = await supabase.auth.getSession();

        if (sessionError) {
          throw new Error(sessionError.message);
        }

        console.log('Successfully authenticated, redirecting to dashboard');
        navigate('/');
      } catch (err: any) {
        console.error('Auth callback error:', err);
        setError(err.message || 'Authentication failed');
        // Wait a moment before redirecting on error
        setTimeout(() => navigate('/login'), 3000);
      }
    };

    handleAuthCallback();
  }, [navigate]);

  return (
    <div className="flex items-center justify-center min-h-screen bg-primary/5">
      <div className="w-full max-w-md p-8 text-center">
        {error ? (
          <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
            <h2 className="text-lg font-medium text-red-600">Authentication Failed</h2>
            <p className="mt-2 text-sm text-red-500">{error}</p>
            <p className="mt-4 text-sm text-slate-500">Redirecting to login page...</p>
          </div>
        ) : (
          <div className="flex flex-col items-center">
            <div className="w-12 h-12 rounded-full border-4 border-primary border-t-transparent animate-spin mb-4"></div>
            <h2 className="text-xl font-medium text-slate-800">Completing Sign In</h2>
            <p className="mt-2 text-sm text-slate-500">Please wait while we verify your credentials...</p>
          </div>
        )}
      </div>
    </div>
  );
}