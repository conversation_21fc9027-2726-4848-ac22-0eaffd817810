import { useLocation } from "wouter";
import { useState, useEffect } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import ContentGenerator from "@/components/admin/ContentGenerator";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { Loader2, ChevronDown, ChevronRight } from "lucide-react";
import { apiRequest } from "@/lib/queryClient";

// Helper functions for fetching data (admin routes return all content)
const fetchChapters = async (topicId: number) => {
  const response = await apiRequest('GET', `/api/admin/topics/${topicId}/chapters`);
  return response.json();
};

const fetchExercises = async (chapterId: number) => {
  const response = await apiRequest('GET', `/api/admin/chapters/${chapterId}/exercises`);
  return response.json();
};

export default function AdminDashboard() {
  const [, setLocation] = useLocation();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [expandedTopics, setExpandedTopics] = useState<Record<number, boolean>>({});
  const [expandedChapters, setExpandedChapters] = useState<Record<number, boolean>>({});

  // Fetch topics (admin route returns all topics including unpublished)
  const { data: topics = [], isLoading: isLoadingTopics, error: topicsError } = useQuery({
    queryKey: ["/api/admin/topics"],
    queryFn: async () => {
      console.log('Fetching admin topics...');
      try {
        const response = await apiRequest('GET', '/api/admin/topics');
        const data = await response.json();
        console.log('Admin topics response:', data);
        return data;
      } catch (error) {
        console.error('Error fetching admin topics:', error);
        throw error;
      }
    },
  });



  // Toggle topic published status
  const updateTopicMutation = useMutation({
    mutationFn: async ({ id, published }: { id: number; published: boolean }) => {
      const response = await apiRequest('PATCH', `/api/topics/${id}`, { published });
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/admin/topics"] });
      toast({
        title: "Topic updated",
        description: "The topic's published status has been updated.",
      });
    },
    onError: (error) => {
      toast({
        title: "Update failed",
        description: error.message,
        variant: "destructive",
      });
    }
  });

  // Toggle chapter published status
  const updateChapterMutation = useMutation({
    mutationFn: async ({ id, published }: { id: number; published: boolean }) => {
      const response = await apiRequest('PATCH', `/api/chapters/${id}`, { published });
      return response.json();
    },
    onSuccess: () => {
      // Invalidate and refetch all related queries
      queryClient.invalidateQueries({
        predicate: (query) => {
          const queryKey = query.queryKey[0] as string;
          return queryKey.includes('/admin/topics') || queryKey.includes('/admin/chapters');
        }
      });

      toast({
        title: "Chapter updated",
        description: "The chapter's published status has been updated.",
      });
    },
    onError: (error) => {
      toast({
        title: "Update failed",
        description: error.message,
        variant: "destructive",
      });
    }
  });

  // Toggle exercise published status
  const updateExerciseMutation = useMutation({
    mutationFn: async ({ id, published }: { id: number; published: boolean }) => {
      console.log(`Updating exercise ${id} to published: ${published}`);
      const response = await apiRequest('PATCH', `/api/exercises/${id}`, { published });
      const result = await response.json();
      console.log('Exercise update result:', result);
      return result;
    },
    onSuccess: () => {
      // Invalidate exercise queries to refresh the data
      queryClient.invalidateQueries({
        predicate: (query) => {
          const queryKey = query.queryKey[0] as string;
          return queryKey.includes('/admin/chapters') && queryKey.includes('exercises');
        }
      });
      toast({
        title: "Exercise updated",
        description: "The exercise's published status has been updated.",
      });
    },
    onError: (error) => {
      toast({
        title: "Update failed",
        description: error.message,
        variant: "destructive",
      });
    }
  });

  // Toggle topic expansion
  const toggleTopicExpansion = (topicId: number) => {
    setExpandedTopics(prev => ({
      ...prev,
      [topicId]: !prev[topicId]
    }));
  };

  // Toggle chapter expansion
  const toggleChapterExpansion = (chapterId: number) => {
    setExpandedChapters(prev => ({
      ...prev,
      [chapterId]: !prev[chapterId]
    }));
  };

  // Handle publishing toggle
  const handleTogglePublished = (type: 'topic' | 'chapter' | 'exercise', id: number, currentStatus: boolean) => {
    console.log(`Toggle ${type} ${id} from ${currentStatus} to ${!currentStatus}`);
    if (type === 'topic') {
      updateTopicMutation.mutate({ id, published: !currentStatus });
    } else if (type === 'chapter') {
      updateChapterMutation.mutate({ id, published: !currentStatus });
    } else {
      updateExerciseMutation.mutate({ id, published: !currentStatus });
    }
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Header />

      <main className="flex-grow container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-primary mb-2">Admin Dashboard</h1>
          <p className="text-gray-600">
            Welcome, Administrator! Manage content and settings here.
          </p>
        </div>

        <Tabs defaultValue="content-management" className="mb-8">
          <TabsList className="mb-4">
            <TabsTrigger value="content-management">Content Management</TabsTrigger>
            <TabsTrigger value="content-generator">Content Generator</TabsTrigger>
            <TabsTrigger value="system-status">System Status</TabsTrigger>
          </TabsList>

          <TabsContent value="content-management">
            <Card>
              <CardHeader>
                <CardTitle>Topics & Exercises</CardTitle>
                <CardDescription>
                  Manage your educational content. Toggle visibility by publishing or unpublishing items.
                </CardDescription>
              </CardHeader>
              <CardContent>
                {isLoadingTopics ? (
                  <div className="flex justify-center p-12">
                    <Loader2 className="h-8 w-8 animate-spin text-primary" />
                  </div>
                ) : topicsError ? (
                  <div className="text-center p-12">
                    <h3 className="text-lg font-semibold mb-2 text-red-600">Error Loading Topics</h3>
                    <p className="text-gray-600 mb-4">{topicsError.message}</p>
                    <Button onClick={() => window.location.reload()}>Retry</Button>
                  </div>
                ) : topics.length === 0 ? (
                  <div className="text-center p-12">
                    <h3 className="text-lg font-semibold mb-2">No Topics Found</h3>
                    <p className="text-gray-600">No topics are available in the database.</p>
                  </div>
                ) : (
                  <div className="border rounded-md">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead className="w-[300px]">Name</TableHead>
                          <TableHead>Category</TableHead>
                          <TableHead>Status</TableHead>
                          <TableHead className="text-right">Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {topics.map((topic: any) => (
                          <>
                            <TableRow key={topic.id} className="hover:bg-muted/50">
                              <TableCell className="font-medium">
                                <button
                                  onClick={() => toggleTopicExpansion(topic.id)}
                                  className="flex items-center gap-2"
                                >
                                  {expandedTopics[topic.id] ? (
                                    <ChevronDown className="h-4 w-4" />
                                  ) : (
                                    <ChevronRight className="h-4 w-4" />
                                  )}
                                  {topic.name}
                                </button>
                              </TableCell>
                              <TableCell>{topic.category}</TableCell>
                              <TableCell>
                                <Badge variant={topic.published ? "default" : "outline"}>
                                  {topic.published ? "Published" : "Draft"}
                                </Badge>
                              </TableCell>
                              <TableCell className="text-right">
                                <div className="flex items-center justify-end gap-2">
                                  <Switch
                                    checked={topic.published}
                                    onCheckedChange={() => handleTogglePublished('topic', topic.id, topic.published)}
                                  />
                                  <span className="text-xs text-muted-foreground">
                                    {topic.published ? "Published" : "Draft"}
                                  </span>
                                </div>
                              </TableCell>
                            </TableRow>

                            {/* Chapters and exercises for this topic */}
                            {expandedTopics[topic.id] && (
                              <TopicChapters
                                topicId={topic.id}
                                expandedChapters={expandedChapters}
                                toggleChapterExpansion={toggleChapterExpansion}
                                handleTogglePublished={handleTogglePublished}
                              />
                            )}
                          </>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="content-generator">
            <div className="grid grid-cols-1 gap-6">
              <ContentGenerator />
            </div>
          </TabsContent>

          <TabsContent value="system-status">
            <div className="bg-white rounded-xl p-6 shadow-sm">
              <div className="flex items-center gap-2 mb-4">
                <div className="w-3 h-3 rounded-full bg-green-500"></div>
                <span>Database Connection: Active</span>
              </div>
              <div className="flex items-center gap-2 mb-4">
                <div className="w-3 h-3 rounded-full bg-green-500"></div>
                <span>OpenAI API: Connected</span>
              </div>
              <div className="flex items-center gap-2 mb-4">
                <div className="w-3 h-3 rounded-full bg-green-500"></div>
                <span>Authentication Service: Active</span>
              </div>
              <p className="text-sm text-gray-500 mt-4">
                Click the "Generate Math Content" button to create educational content for math topics.
                This will use OpenAI to generate age-appropriate lessons and exercises.
              </p>
            </div>
          </TabsContent>
        </Tabs>

        <div className="mt-8">
          <button
            onClick={() => setLocation("/")}
            className="flex items-center gap-2 text-primary hover:underline"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="h-4 w-4"
            >
              <path d="m15 18-6-6 6-6" />
            </svg>
            Return to Dashboard
          </button>
        </div>
      </main>

      <Footer />
    </div>
  );
}

// Component to display chapters for a topic
function TopicChapters({
  topicId,
  expandedChapters,
  toggleChapterExpansion,
  handleTogglePublished
}: {
  topicId: number;
  expandedChapters: Record<number, boolean>;
  toggleChapterExpansion: (id: number) => void;
  handleTogglePublished: (type: 'topic' | 'chapter' | 'exercise', id: number, currentStatus: boolean) => void;
}) {
  const { data: chapters = [], isLoading } = useQuery({
    queryKey: [`/api/admin/topics/${topicId}/chapters`],
    queryFn: () => fetchChapters(topicId),
  });

  if (isLoading) {
    return (
      <TableRow>
        <TableCell colSpan={4} className="h-24 text-center">
          <Loader2 className="h-5 w-5 animate-spin mx-auto" />
        </TableCell>
      </TableRow>
    );
  }

  return (
    <>
      {chapters.map((chapter: any) => (
        <>
          <TableRow key={chapter.id} className="bg-muted/30">
            <TableCell className="pl-10 font-medium">
              <button
                onClick={() => toggleChapterExpansion(chapter.id)}
                className="flex items-center gap-2"
              >
                {expandedChapters[chapter.id] ? (
                  <ChevronDown className="h-4 w-4" />
                ) : (
                  <ChevronRight className="h-4 w-4" />
                )}
                {chapter.title}
              </button>
            </TableCell>
            <TableCell>Chapter</TableCell>
            <TableCell>
              <Badge variant={chapter.published ? "default" : "outline"}>
                {chapter.published ? "Published" : "Draft"}
              </Badge>
            </TableCell>
            <TableCell className="text-right">
              <div className="flex items-center justify-end gap-2">
                <Switch
                  checked={chapter.published}
                  onCheckedChange={() => handleTogglePublished('chapter', chapter.id, chapter.published)}
                />
                <span className="text-xs text-muted-foreground">
                  {chapter.published ? "Published" : "Draft"}
                </span>
              </div>
            </TableCell>
          </TableRow>

          {/* Exercises for this chapter */}
          {expandedChapters[chapter.id] && (
            <ChapterExercises
              chapterId={chapter.id}
              handleTogglePublished={handleTogglePublished}
            />
          )}
        </>
      ))}
    </>
  );
}

// Component to display exercises for a chapter
function ChapterExercises({
  chapterId,
  handleTogglePublished
}: {
  chapterId: number;
  handleTogglePublished: (type: 'topic' | 'chapter' | 'exercise', id: number, currentStatus: boolean) => void;
}) {
  const { data: exercises = [], isLoading } = useQuery({
    queryKey: [`/api/admin/chapters/${chapterId}/exercises`],
    queryFn: () => fetchExercises(chapterId),
  });

  if (isLoading) {
    return (
      <TableRow>
        <TableCell colSpan={4} className="h-24 text-center">
          <Loader2 className="h-5 w-5 animate-spin mx-auto" />
        </TableCell>
      </TableRow>
    );
  }

  return (
    <>
      {exercises.map((exercise: any) => (
        <TableRow key={exercise.id} className="bg-muted/10">
          <TableCell className="pl-16 font-medium">
            {exercise.title}
          </TableCell>
          <TableCell>
            <Badge variant="outline" className="bg-muted/50">
              {exercise.difficulty}
            </Badge>
          </TableCell>
          <TableCell>
            <Badge variant={exercise.published ? "default" : "outline"}>
              {exercise.published ? "Published" : "Draft"}
            </Badge>
          </TableCell>
          <TableCell className="text-right">
            <div className="flex items-center justify-end gap-2">
              <Switch
                checked={exercise.published}
                onCheckedChange={() => handleTogglePublished('exercise', exercise.id, exercise.published)}
              />
              <span className="text-xs text-muted-foreground">
                {exercise.published ? "Published" : "Draft"}
              </span>
            </div>
          </TableCell>
        </TableRow>
      ))}
    </>
  );
}
