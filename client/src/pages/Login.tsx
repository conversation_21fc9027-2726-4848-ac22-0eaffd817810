import Footer from "@/components/Footer";
import { Button } from "@/components/ui/button";
import { useContext, useEffect } from "react";
import { useLocation } from "wouter";
import { UserContext } from "@/App";

export default function Login() {
  const [, navigate] = useLocation();
  const { isAuthenticated, isLoading } = useContext(UserContext);
  
  useEffect(() => {
    if (isAuthenticated) {
      // User is already authenticated, redirect to dashboard
      navigate("/");
    }
  }, [isAuthenticated, navigate]);

  const handleLogin = () => {
    // Redirect to Replit Auth login
    window.location.href = "/api/login";
  };

  return (
    <div className="min-h-screen flex flex-col">
      <header className="bg-white shadow-md">
        <div className="container mx-auto px-4 py-3 flex justify-center">
          <div className="flex items-center">
            <svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg" className="mr-3">
              <rect width="40" height="40" rx="20" fill="#4B7BEC" />
              <path d="M12 20H28M20 12V28" stroke="white" strokeWidth="4" strokeLinecap="round" />
            </svg>
            <h1 className="font-baloo font-bold text-2xl text-primary">MathFun</h1>
          </div>
        </div>
      </header>
      
      <main className="flex-grow container mx-auto px-4 py-12 flex flex-col items-center justify-center">
        <div className="w-full max-w-md rounded-xl bg-white p-8 shadow-lg">
          <div className="mb-6 text-center">
            <h1 className="text-3xl font-bold text-primary">Welcome to MathFun</h1>
            <p className="mt-2 text-muted-foreground">
              Learning math has never been more fun!
            </p>
          </div>
          
          <div className="space-y-4">
            <p className="text-center text-sm text-muted-foreground">
              Sign in to access your learning journey
            </p>
            
            <Button
              onClick={handleLogin}
              disabled={isLoading}
              className="w-full"
            >
              {isLoading ? "Loading..." : "Sign in with Replit"}
            </Button>
            
            <div className="text-center text-xs text-muted-foreground">
              <p>Secure login powered by Replit</p>
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
}
