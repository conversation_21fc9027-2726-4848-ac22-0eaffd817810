import { apiRequest } from "./queryClient";

// Types for client-side API interaction
export interface Topic {
  id: number;
  name: string;
  description: string;
  category: string;
  imageUrl?: string;
}

export interface Chapter {
  id: number;
  topicId: number;
  title: string;
  description: string;
  order: number;
  durationMinutes: number;
  exerciseCount: number;
  pointsReward: number;
}

export interface Exercise {
  id: number;
  chapterId: number;
  title: string;
  description: string;
  difficulty: string;
  content: any;
  type: string;
  order: number;
}

export interface UserProgress {
  id: number;
  userId: number;
  chapterId: number;
  completed: boolean;
  completedAt?: Date;
  score?: number;
}

export interface Achievement {
  id: number;
  title: string;
  description: string;
  icon: string;
  color: string;
}

export interface UserAchievement {
  id: number;
  userId: number;
  achievementId: number;
  unlockedAt: Date;
}

// User Authentication
export async function registerUser(username: string, password: string, age: number, parentEmail?: string) {
  try {
    const response = await apiRequest("POST", "/api/auth/register", {
      username,
      password,
      age,
      parentEmail
    });
    const data = await response.json();
    console.log("Register response:", data);
    return data;
  } catch (error) {
    console.error("Register error:", error);
    throw error;
  }
}

export async function loginUser(username: string, password: string) {
  try {
    const response = await apiRequest("POST", "/api/auth/login", {
      username,
      password
    });
    const data = await response.json();
    console.log("Login response:", data);
    return data;
  } catch (error) {
    console.error("Login error:", error);
    throw error;
  }
}

// Topics
export async function createTopic(name: string, category: string, description?: string, imageUrl?: string) {
  const response = await apiRequest("POST", "/api/topics", {
    name,
    category,
    description,
    imageUrl
  });
  return response.json();
}

// Chapters
export async function createChapter(
  topicId: number, 
  title: string, 
  order: number,
  description?: string,
  durationMinutes?: number,
  exerciseCount?: number,
  pointsReward?: number
) {
  const response = await apiRequest("POST", "/api/chapters", {
    topicId,
    title,
    order,
    description,
    durationMinutes,
    exerciseCount,
    pointsReward
  });
  return response.json();
}

// User Progress
export async function updateProgress(
  userId: number,
  chapterId: number,
  completed: boolean,
  score?: number
) {
  const data: any = {
    userId,
    chapterId,
    completed
  };
  
  if (completed) {
    data.completedAt = new Date();
  }
  
  if (score !== undefined) {
    data.score = score;
  }
  
  const response = await apiRequest("POST", "/api/progress", data);
  return response.json();
}

// Initialize default data
export async function initializeDefaultData() {
  const response = await apiRequest("POST", "/api/init", {});
  return response.json();
}
