import { useEffect, useState } from "react";
import { useLocation } from "wouter";
import { Button } from "@/components/ui/button";
import {
  NavigationMenu,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  navigationMenuTriggerStyle,
} from "@/components/ui/navigation-menu";
import { cn } from "@/lib/utils";
import { Chapter, Topic } from "@shared/schema";

interface TopNavigationProps {
  topics: Topic[];
  chapters: Chapter[];
  currentTopicId?: number;
}

export default function TopNavigation({ 
  topics, 
  chapters, 
  currentTopicId 
}: TopNavigationProps) {
  const [, navigate] = useLocation();
  const [selectedTopicId, setSelectedTopicId] = useState<number | undefined>(currentTopicId);
  const [filteredChapters, setFilteredChapters] = useState<Chapter[]>([]);
  
  // Filter chapters based on selected topic
  useEffect(() => {
    if (selectedTopicId) {
      const filtered = chapters.filter(chapter => chapter.topicId === selectedTopicId);
      setFilteredChapters(filtered);
    } else {
      setFilteredChapters(chapters);
    }
  }, [selectedTopicId, chapters]);

  // Handle topic selection
  const handleTopicSelect = (topicId: number) => {
    setSelectedTopicId(topicId);
  };
  
  // Navigate to chapter
  const handleChapterSelect = (chapterId: number) => {
    navigate(`/chapter/${chapterId}`);
  };

  return (
    <div className="bg-white shadow-sm mb-6">
      <div className="container mx-auto py-2 px-4">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
          {/* Topic Filter */}
          <div className="flex flex-wrap gap-2">
            <Button 
              variant={!selectedTopicId ? "default" : "outline"} 
              size="sm"
              onClick={() => setSelectedTopicId(undefined)}
            >
              All Topics
            </Button>
            
            {topics.map(topic => (
              <Button 
                key={topic.id} 
                variant={selectedTopicId === topic.id ? "default" : "outline"} 
                size="sm"
                onClick={() => handleTopicSelect(topic.id)}
              >
                {topic.name}
              </Button>
            ))}
          </div>
          
          {/* Chapter Navigation */}
          <NavigationMenu>
            <NavigationMenuList>
              <NavigationMenuItem>
                <div className="text-sm font-medium text-muted-foreground mr-3">
                  Chapters:
                </div>
              </NavigationMenuItem>
              
              {filteredChapters.length > 0 ? (
                filteredChapters.map(chapter => (
                  <NavigationMenuItem key={chapter.id}>
                    <NavigationMenuLink
                      className={cn(
                        navigationMenuTriggerStyle(),
                        "cursor-pointer text-sm py-1.5"
                      )}
                      onClick={() => handleChapterSelect(chapter.id)}
                    >
                      {chapter.title}
                    </NavigationMenuLink>
                  </NavigationMenuItem>
                ))
              ) : (
                <NavigationMenuItem>
                  <div className="text-sm text-muted-foreground">
                    No chapters available
                  </div>
                </NavigationMenuItem>
              )}
            </NavigationMenuList>
          </NavigationMenu>
        </div>
      </div>
    </div>
  );
}