import { Chapter, Topic } from "@shared/schema";
import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";

interface ChapterIntroProps {
  chapter: Chapter;
  topic?: Topic;
  onStartLearning: () => void;
}

export default function ChapterIntro({
  chapter,
  topic,
  onStartLearning
}: ChapterIntroProps) {
  return (
    <section className="bg-white rounded-2xl p-6 shadow-sm mb-8">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        <div className="col-span-2">
          <div className="flex items-center gap-2 mb-2">
            <div 
              className={cn(
                "text-xs font-medium px-3 py-1 rounded-full",
                topic?.category === "arithmetic" 
                  ? "bg-primary/10 text-primary" 
                  : "bg-secondary/10 text-secondary"
              )}
            >
              {topic?.category || "Mathematics"}
            </div>
            <div className="text-xs font-medium px-3 py-1 rounded-full bg-gray-100 text-gray-600">
              Chapter {chapter.order}
            </div>
          </div>
          
          <h3 className="font-baloo font-semibold text-3xl mb-4">
            {chapter.title}
          </h3>
          
          <div className="prose prose-sm max-w-none mb-6">
            <p className="text-gray-700">{chapter.description}</p>
            
            <h4 className="font-semibold text-lg mt-6 mb-3">What you'll learn:</h4>
            <ul className="space-y-2">
              {chapter.description.split('. ').slice(0, 3).map((item, idx) => (
                <li key={idx} className="flex items-start gap-2">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5 text-success shrink-0 mt-0.5">
                    <polyline points="20 6 9 17 4 12"></polyline>
                  </svg>
                  <span>{item.trim()}</span>
                </li>
              ))}
            </ul>
          </div>
          
          <div className="mt-6 flex flex-wrap gap-3">
            <div className="bg-primary bg-opacity-10 rounded-xl px-4 py-2 text-primary font-medium">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="h-4 w-4 inline-block mr-1"
              >
                <circle cx="12" cy="12" r="10" />
                <polyline points="12 6 12 12 16 14" />
              </svg>
              {chapter.durationMinutes} minutes
            </div>
            <div className="bg-accent bg-opacity-10 rounded-xl px-4 py-2 text-accent font-medium">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="h-4 w-4 inline-block mr-1"
              >
                <path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z" />
                <path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z" />
              </svg>
              {chapter.exerciseCount} exercises
            </div>
            <div className="bg-success bg-opacity-10 rounded-xl px-4 py-2 text-success font-medium">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="h-4 w-4 inline-block mr-1"
              >
                <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2" />
              </svg>
              Earn {chapter.pointsReward} points
            </div>
          </div>
          
          <Button 
            className="mt-8 px-8 py-6 text-lg font-medium"
            onClick={onStartLearning}
          >
            Start Learning
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="ml-2">
              <path d="M5 12h14"></path>
              <path d="m12 5 7 7-7 7"></path>
            </svg>
          </Button>
        </div>
        <div className="flex justify-center items-center">
          <img
            src={
              topic?.category === "arithmetic"
                ? "https://cdn.pixabay.com/photo/2015/11/03/09/03/mathematics-1019790_1280.jpg"
                : "https://cdn.pixabay.com/photo/2022/12/19/03/40/geometry-7664526_1280.jpg"
            }
            alt={`${chapter.title} illustration`}
            className="rounded-xl shadow-md max-h-64 object-cover"
          />
        </div>
      </div>
    </section>
  );
}