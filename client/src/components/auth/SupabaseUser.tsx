import { createContext } from 'react';
import { User } from "@shared/schema";

// Define a context for authentication
export interface UserContextType {
  user: User | null | undefined;
  isLoading: boolean;
  isAuthenticated: boolean;
}

const UserContextValue: UserContextType = {
  user: undefined,
  isLoading: false,
  isAuthenticated: false,
};

export const UserContext = createContext<UserContextType>(UserContextValue);