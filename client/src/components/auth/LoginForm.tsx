import { useContext, useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useToast } from "@/hooks/use-toast";
import { UserContext } from "../../App";
import { loginUser, registerUser } from "../../lib/openai";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useLocation } from "wouter";

const formSchema = z.object({
  username: z.string().min(2, "Name must be at least 2 characters"),
  age: z.string().min(1, "Please select your age"),
  parentEmail: z.string().email("Invalid email").or(z.literal("")),
  password: z.string().min(4, "Password must be at least 4 characters"),
});

export default function LoginForm() {
  const { setUser } = useContext(UserContext);
  const { toast } = useToast();
  const [, setLocation] = useLocation();
  const [isRegistering, setIsRegistering] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      username: "",
      age: "",
      parentEmail: "",
      password: "",
    },
  });

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    setIsLoading(true);
    try {
      let userData;
      
      if (isRegistering) {
        userData = await registerUser(
          values.username,
          values.password,
          parseInt(values.age),
          values.parentEmail || undefined
        );
      } else {
        userData = await loginUser(values.username, values.password);
      }
      
      // Store user in localStorage for persistence
      localStorage.setItem("mathfun_user", JSON.stringify(userData));
      
      // Update context
      setUser(userData);
      
      // Show success toast
      toast({
        title: isRegistering ? "Registration successful!" : "Login successful!",
        description: `Welcome, ${userData.username}!`,
        variant: "default",
      });
      
      console.log("Login successful, redirecting to dashboard");
      
      // Redirect to dashboard with a full page refresh after a short delay
      setTimeout(() => {
        window.location.href = "/";
      }, 500);
    } catch (error) {
      console.error("Authentication error:", error);
      toast({
        title: "Authentication failed",
        description: isRegistering 
          ? "Could not create your account. Please try again." 
          : "Invalid username or password.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="max-w-md mx-auto">
      <div className="text-center mb-8">
        <h2 className="font-baloo font-bold text-3xl mb-2">
          {isRegistering ? "Join MathFun!" : "Welcome to MathFun!"}
        </h2>
        <p className="text-text-light">Let's start learning math the fun way!</p>
      </div>
      
      <div className="bg-white rounded-2xl p-8 shadow-sm">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="username"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="font-medium">Child's Name</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter your name"
                      className="px-4 py-3 rounded-xl"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="password"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="font-medium">Password</FormLabel>
                  <FormControl>
                    <Input
                      type="password"
                      placeholder="Enter your password"
                      className="px-4 py-3 rounded-xl"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            {isRegistering && (
              <>
                <FormField
                  control={form.control}
                  name="age"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="font-medium">Age</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger className="px-4 py-3 rounded-xl">
                            <SelectValue placeholder="Select your age" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="5">5 years</SelectItem>
                          <SelectItem value="6">6 years</SelectItem>
                          <SelectItem value="7">7 years</SelectItem>
                          <SelectItem value="8">8 years</SelectItem>
                          <SelectItem value="9">9 years</SelectItem>
                          <SelectItem value="10">10 years</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="parentEmail"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="font-medium">Parent's Email (Optional)</FormLabel>
                      <FormControl>
                        <Input
                          type="email"
                          placeholder="<EMAIL>"
                          className="px-4 py-3 rounded-xl"
                          {...field}
                        />
                      </FormControl>
                      <p className="text-xs text-text-light mt-1">For progress reports and updates</p>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </>
            )}
            
            <Button
              type="submit"
              className="w-full bg-primary text-white font-semibold py-3 px-6 rounded-xl hover:bg-opacity-90 transition-colors text-lg h-auto"
              disabled={isLoading}
            >
              {isLoading 
                ? "Loading..." 
                : isRegistering 
                  ? "Start Learning!" 
                  : "Login"}
            </Button>
          </form>
        </Form>
        
        <div className="mt-4 text-center">
          <button
            type="button"
            className="text-primary text-sm font-medium"
            onClick={() => setIsRegistering(!isRegistering)}
          >
            {isRegistering
              ? "Already have an account? Login"
              : "New to MathFun? Register now"}
          </button>
        </div>
      </div>
      
      <div className="mt-8 rounded-2xl overflow-hidden">
        <img
          src="https://cdn.pixabay.com/photo/2017/07/31/11/44/laptop-2557576_1280.jpg"
          alt="Children learning math together"
          className="w-full object-cover h-64"
        />
      </div>
    </div>
  );
}
