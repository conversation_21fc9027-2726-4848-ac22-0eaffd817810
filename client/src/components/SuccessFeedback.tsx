import { Button } from "@/components/ui/button";

interface SuccessFeedbackProps {
  message: string;
  detail: string;
  onNext: () => void;
}

export default function SuccessFeedback({
  message,
  detail,
  onNext
}: SuccessFeedbackProps) {
  return (
    <section className="bg-success bg-opacity-10 rounded-2xl p-6 shadow-sm mb-8">
      <div className="flex items-center">
        <div className="bg-success rounded-full p-3 mr-4">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="3"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="h-6 w-6 text-white"
          >
            <polyline points="20 6 9 17 4 12" />
          </svg>
        </div>
        <div>
          <h3 className="font-baloo font-semibold text-xl text-success mb-1">
            {message}
          </h3>
          <p>{detail}</p>
        </div>
      </div>
      <div className="flex justify-end mt-4">
        <Button 
          className="bg-success text-white font-semibold hover:bg-opacity-90 transition-colors"
          onClick={onNext}
        >
          Next Exercise 
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="h-4 w-4 ml-1"
          >
            <path d="M5 12h14" />
            <path d="m12 5 7 7-7 7" />
          </svg>
        </Button>
      </div>
    </section>
  );
}
