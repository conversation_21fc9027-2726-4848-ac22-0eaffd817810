import { useState, useEffect } from "react";
import { useLocation } from "wouter";
import { Topic, Chapter } from "@shared/schema";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

interface TopicSelectionProps {
  topics: Topic[];
  topicChapters: Record<number, Chapter[]>;
  currentTopicId?: number;
}

export default function TopicSelection({
  topics,
  topicChapters,
  currentTopicId,
}: TopicSelectionProps) {
  const [, navigate] = useLocation();
  const [selectedTopic, setSelectedTopic] = useState<number | undefined>(
    currentTopicId
  );

  const handleTopicSelect = (topicId: number) => {
    setSelectedTopic(topicId);
  };

  const handleChapterSelect = (chapterId: number) => {
    navigate(`/chapter/${chapterId}`);
  };

  // Get chapters for the selected topic
  const chapters = selectedTopic ? topicChapters[selectedTopic] || [] : [];

  return (
    <div className="rounded-xl bg-white shadow-sm p-6 mb-8">
      <h2 className="font-bold text-xl mb-4">Choose a Topic</h2>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        {topics.length > 0 ? topics.map((topic) => (
          <div
            key={topic.id}
            className={cn(
              "border rounded-lg p-4 cursor-pointer transition-colors",
              selectedTopic === topic.id
                ? "border-primary bg-primary/5"
                : "border-gray-200 hover:border-primary/50"
            )}
            onClick={() => handleTopicSelect(topic.id)}
          >
            <div className="flex items-center gap-3">
              <div 
                className={cn(
                  "w-10 h-10 rounded-full flex items-center justify-center text-white",
                  topic.category?.toLowerCase().includes("arith") ? "bg-primary" : "bg-secondary"
                )}
              >
                {topic.category?.toLowerCase().includes("arith") ? (
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M5 12h14" />
                    <path d="M12 5v14" />
                  </svg>
                ) : (
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M3.34 17a10 10 0 1 1 17.32 0" />
                    <path d="M3 17h18" />
                  </svg>
                )}
              </div>
              <div>
                <h3 className="font-semibold text-lg">{topic.name}</h3>
                <p className="text-sm text-gray-500">{topic.category}</p>
              </div>
            </div>
          </div>
        )) : (
          <div className="col-span-2 p-6 text-center border rounded-lg border-dashed">
            <p className="text-gray-500">No topics available. Try creating some in the admin dashboard.</p>
          </div>
        )}
      </div>

      {selectedTopic && (
        <div>
          <h3 className="font-semibold text-lg mb-3">Available Chapters</h3>
          {chapters && chapters.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
              {chapters.map((chapter) => (
                <Button
                  key={chapter.id}
                  variant="outline"
                  className="justify-between group"
                  onClick={() => handleChapterSelect(chapter.id)}
                >
                  <span>Chapter {chapter.order}: {chapter.title}</span>
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="ml-2 opacity-70 group-hover:opacity-100 transition-opacity">
                    <path d="m9 18 6-6-6-6" />
                  </svg>
                </Button>
              ))}
            </div>
          ) : (
            <div className="p-6 text-center border rounded-lg border-dashed">
              <p className="text-gray-500">No chapters available for this topic yet.</p>
              <p className="text-sm text-gray-400 mt-2">Try generating content for this topic in the admin dashboard.</p>
            </div>
          )}
        </div>
      )}
    </div>
  );
}