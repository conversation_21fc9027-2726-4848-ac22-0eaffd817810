import { useContext } from "react";
import { <PERSON> } from "wouter";
import { UserContext } from "../App";

export default function Header() {
  const { user, isAuthenticated } = useContext(UserContext);
  
  // Get display name from user object
  const getDisplayName = () => {
    if (!user) return "";
    if (user.firstName) return user.firstName;
    if (user.email) return user.email.split('@')[0];
    return "User";
  };
  
  // Get initial for avatar
  const getInitial = () => {
    const displayName = getDisplayName();
    return displayName.charAt(0).toUpperCase();
  };
  
  const handleLogout = () => {
    window.location.href = "/api/logout";
  };
  
  return (
    <header className="bg-white shadow-md">
      <div className="container mx-auto px-4 py-3 flex justify-between items-center">
        <div className="flex items-center">
          <Link href="/" className="flex items-center">
            <svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg" className="mr-3">
              <rect width="40" height="40" rx="20" fill="#4B7BEC" />
              <path d="M12 20H28M20 12V28" stroke="white" strokeWidth="4" strokeLinecap="round" />
            </svg>
            <h1 className="font-baloo font-bold text-2xl text-primary">MathFun</h1>
          </Link>
        </div>
        {isAuthenticated && user && (
          <div className="flex items-center">
            <div className="hidden md:flex items-center mr-4">
              <span className="font-nunito font-semibold">{getDisplayName()}</span>
              <div className="w-8 h-8 bg-accent rounded-full ml-2 flex items-center justify-center">
                {user.profileImageUrl ? (
                  <img 
                    src={user.profileImageUrl} 
                    alt="Profile" 
                    className="w-8 h-8 rounded-full object-cover" 
                  />
                ) : (
                  <span className="font-baloo font-bold text-white">
                    {getInitial()}
                  </span>
                )}
              </div>
            </div>
            <button 
              onClick={handleLogout}
              className="text-primary hover:text-opacity-80 p-2 rounded-full bg-primary bg-opacity-10"
              title="Logout"
            >
              <svg 
                xmlns="http://www.w3.org/2000/svg" 
                viewBox="0 0 24 24" 
                fill="none" 
                stroke="currentColor" 
                strokeWidth="2" 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                className="h-5 w-5"
              >
                <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4" />
                <polyline points="16 17 21 12 16 7" />
                <line x1="21" y1="12" x2="9" y2="12" />
              </svg>
            </button>
          </div>
        )}
      </div>
    </header>
  );
}
