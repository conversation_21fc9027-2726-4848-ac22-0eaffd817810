interface ProgressCardProps {
  title: string;
  percentage: number;
  completedCount: number;
  totalCount: number;
  color: string;
}

export default function ProgressCard({ 
  title, 
  percentage, 
  completedCount, 
  totalCount, 
  color 
}: ProgressCardProps) {
  const bgColor = `bg-${color}`;
  const bgOpacityColor = `bg-${color} bg-opacity-10`;
  const textColor = `text-${color}`;
  
  return (
    <div className={bgOpacityColor + " rounded-xl p-4"}>
      <div className="flex justify-between items-center mb-2">
        <h4 className="font-baloo font-medium text-lg">{title}</h4>
        <span className={textColor + " font-semibold"}>{percentage}%</span>
      </div>
      <div className="h-3 bg-white rounded-full overflow-hidden">
        <div 
          className={`h-full ${bgColor} rounded-full transition-all duration-500 ease-in-out`} 
          style={{ width: `${percentage}%` }}
        ></div>
      </div>
      <p className="mt-3 text-sm text-text-light">
        {completedCount} of {totalCount} chapters completed
      </p>
    </div>
  );
}
