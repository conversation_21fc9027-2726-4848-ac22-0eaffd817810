import { useState, useContext } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import SuccessFeedback from "./SuccessFeedback";
import { useMutation } from "@tanstack/react-query";
import { UserContext } from "../App";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";

interface ExerciseCardProps {
  exerciseNumber: number;
  totalExercises: number;
  title: string;
  description: string;
  difficulty: string;
  content: any;
  type: string;
  exerciseId: number;
  onComplete: (score: number) => void;
}

export default function ExerciseCard({
  exerciseNumber,
  totalExercises,
  title,
  description,
  difficulty,
  content,
  type,
  exerciseId,
  onComplete,
}: ExerciseCardProps) {
  const { user } = useContext(UserContext);
  const { toast } = useToast();
  const [selectedAnswer, setSelectedAnswer] = useState<number | string | null>(null);
  const [isCorrect, setIsCorrect] = useState<boolean | null>(null);
  const [showFeedback, setShowFeedback] = useState(false);
  const [attempts, setAttempts] = useState(0);
  const [fillInAnswer, setFillInAnswer] = useState("");
  const [matchingPairs, setMatchingPairs] = useState<{[key: string]: string}>({});
  const [selectedTerm, setSelectedTerm] = useState<string | null>(null);

  // Mutation for submitting student answers
  const submitAnswerMutation = useMutation({
    mutationFn: async (data: {
      userId: string;
      exerciseId: number;
      answer: string | number;
    }) => {
      return apiRequest<{isCorrect: boolean, studentAnswer: any}>("/api/student-answers", {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify(data)
      });
    },
    onSuccess: (data) => {
      // Use the server's validation of correctness
      setIsCorrect(data.isCorrect);
      setShowFeedback(true);

      if (data.isCorrect) {
        // Calculate score based on difficulty and attempts
        const scoreMap = { easy: 5, medium: 10, hard: 15 };
        const baseScore = scoreMap[difficulty as keyof typeof scoreMap] || 5;
        // Reduce score for multiple attempts
        const attemptsDeduction = Math.max(0, attempts - 1) * 2;
        const finalScore = Math.max(1, baseScore - attemptsDeduction);

        // Delay completing the exercise to show feedback
        setTimeout(() => {
          onComplete(finalScore);
        }, 2000);
      } else {
        // If incorrect, allow another attempt
        toast({
          title: "Not quite right",
          description: "Try again! Learning happens through practice.",
          variant: "destructive",
        });
      }
    },
    onError: (error) => {
      console.error("Error submitting answer:", error);
      toast({
        title: "Error submitting answer",
        description: "There was a problem saving your answer. Please try again.",
        variant: "destructive",
      });
    }
  });

  const difficultyColor = {
    easy: "text-accent",
    medium: "text-primary",
    hard: "text-secondary"
  }[difficulty] || "text-accent";

  const difficultyBgColor = {
    easy: "bg-accent bg-opacity-10",
    medium: "bg-primary bg-opacity-10",
    hard: "bg-secondary bg-opacity-10"
  }[difficulty] || "bg-accent bg-opacity-10";

  const checkAnswer = (answer: number | string | object) => {
    setSelectedAnswer(answer as number | string);
    setAttempts(prev => prev + 1);

    // Submit the answer to the server for validation
    if (user) {
      submitAnswerMutation.mutate({
        userId: user.id,
        exerciseId,
        answer: answer as string | number
      });
    } else {
      // Fallback local validation if user context is not available
      let correct = false;

      if (type === 'multiple-choice') {
        correct = answer === content.correctAnswer;
      } else if (type === 'fill-in-blank') {
        correct = String(answer).toLowerCase().trim() === String(content.answer || content.correctAnswer).toLowerCase().trim();
      } else if (type === 'matching') {
        // For matching, answer should be an object with all correct pairs
        const answerObj = answer as {[key: string]: string};
        correct = content.pairs.every((pair: any) =>
          answerObj[pair.term] === pair.definition
        );
      } else {
        // Arithmetic and other types
        correct = answer === content.correctAnswer;
      }

      setIsCorrect(correct);
      setShowFeedback(true);

      if (correct) {
        // Calculate score based on difficulty
        const scoreMap = { easy: 5, medium: 10, hard: 15 };
        const score = scoreMap[difficulty as keyof typeof scoreMap] || 5;

        // Delay completing the exercise to show feedback
        setTimeout(() => {
          onComplete(score);
        }, 2000);
      }
    }
  };

  const renderArithmeticExercise = () => {
    if (!content.numbers) return null;

    const getVisualElements = (num: number, colorClass: string) => {
      if (type === 'addition' || type === 'subtraction') {
        // Show visual objects for addition/subtraction
        return Array.from({ length: Math.min(num, 10) }).map((_, i) => (
          <div
            key={i}
            className={`w-8 h-8 rounded-full ${colorClass} border-2 border-white shadow-sm m-1 flex items-center justify-center`}
          >
            <span className="text-white text-xs font-bold">{i + 1}</span>
          </div>
        ));
      }
      return null;
    };

    return (
      <div className="bg-gradient-to-r from-yellow-50 to-orange-50 rounded-xl p-6 mb-6">
        <div className="flex justify-center items-center gap-8 mb-6">
          {content.numbers.map((num: number, index: number) => (
            <div key={index} className="flex flex-col items-center">
              {/* Visual representation */}
              {(type === 'addition' || type === 'subtraction') && num <= 10 && (
                <div className="flex flex-wrap justify-center max-w-[120px] mb-4">
                  {getVisualElements(num, index === 0 ? 'bg-blue-500' : 'bg-green-500')}
                </div>
              )}

              {/* Number display */}
              <div className="bg-white rounded-xl p-4 shadow-md border-2 border-gray-200">
                <span className="text-3xl font-bold text-gray-800">{num}</span>
              </div>
            </div>
          ))}

          {/* Operation symbol */}
          <div className="bg-primary text-white rounded-full w-16 h-16 flex items-center justify-center shadow-lg">
            <span className="text-3xl font-bold">
              {type === 'addition' && '+'}
              {type === 'subtraction' && '-'}
              {type === 'multiplication' && '×'}
              {type === 'division' && '÷'}
            </span>
          </div>

          {/* Equals sign */}
          <div className="text-3xl font-bold text-gray-600">=</div>

          {/* Answer placeholder */}
          <div className="flex flex-col items-center">
            <div className="h-20 w-20 rounded-xl border-4 border-dashed border-primary bg-white flex items-center justify-center shadow-md">
              <span className="text-3xl font-bold text-primary">?</span>
            </div>
          </div>
        </div>

        {/* Helpful hint for children */}
        <div className="text-center">
          <p className="text-sm text-gray-600 font-medium">
            {type === 'addition' && "Count all the objects together!"}
            {type === 'subtraction' && "Take away the second number from the first!"}
            {type === 'multiplication' && "Add the first number to itself the second number of times!"}
            {type === 'division' && "Split the first number into equal groups!"}
          </p>
        </div>
      </div>
    );
  };

  const renderGeometryExercise = () => {
    if (!content.shapes) return null;

    const getShapeColor = (color: string) => {
      const colorMap: {[key: string]: string} = {
        'red': 'bg-red-500',
        'blue': 'bg-blue-500',
        'green': 'bg-green-500',
        'yellow': 'bg-yellow-500',
        'purple': 'bg-purple-500',
        'orange': 'bg-orange-500',
        'pink': 'bg-pink-500'
      };
      return colorMap[color] || 'bg-gray-500';
    };

    return (
      <div className="bg-gradient-to-r from-purple-50 to-blue-50 rounded-xl p-6 mb-6">
        <div className="flex justify-center items-center gap-8 mb-6 flex-wrap">
          {content.shapes.map((shape: any, index: number) => (
            <div key={index} className="flex flex-col items-center p-4 bg-white rounded-xl shadow-md border-2 border-gray-200">
              <div className="w-32 h-32 flex items-center justify-center mb-4 relative">
                {shape.name === 'circle' && (
                  <div className={`w-24 h-24 rounded-full ${getShapeColor(shape.color)} shadow-lg border-4 border-white`}></div>
                )}
                {shape.name === 'square' && (
                  <div className={`w-24 h-24 ${getShapeColor(shape.color)} shadow-lg border-4 border-white rounded-lg`}></div>
                )}
                {shape.name === 'triangle' && (
                  <div
                    className={`w-24 h-24 ${getShapeColor(shape.color)} shadow-lg border-4 border-white`}
                    style={{ clipPath: 'polygon(50% 0%, 0% 100%, 100% 100%)' }}
                  ></div>
                )}
                {shape.name === 'rectangle' && (
                  <div className={`w-32 h-20 ${getShapeColor(shape.color)} shadow-lg border-4 border-white rounded-lg`}></div>
                )}
                {shape.name === 'star' && (
                  <div
                    className={`w-24 h-24 ${getShapeColor(shape.color)} shadow-lg border-4 border-white`}
                    style={{ clipPath: 'polygon(50% 0%, 61% 35%, 98% 35%, 68% 57%, 79% 91%, 50% 70%, 21% 91%, 32% 57%, 2% 35%, 39% 35%)' }}
                  ></div>
                )}
                {shape.name === 'hexagon' && (
                  <div
                    className={`w-24 h-24 ${getShapeColor(shape.color)} shadow-lg border-4 border-white`}
                    style={{ clipPath: 'polygon(25% 0%, 75% 0%, 100% 50%, 75% 100%, 25% 100%, 0% 50%)' }}
                  ></div>
                )}
              </div>
              <div className="text-center">
                <span className="text-lg font-bold capitalize text-gray-800">{shape.name}</span>
                <br />
                <span className="text-sm font-medium capitalize text-gray-600">{shape.color}</span>
              </div>
            </div>
          ))}
        </div>

        {/* Helpful hint for children */}
        <div className="text-center">
          <p className="text-sm text-gray-600 font-medium">
            Look carefully at the shapes and their colors!
          </p>
        </div>
      </div>
    );
  };

  const renderMultipleChoiceExercise = () => {
    if (!content.question || !content.options) return null;

    return (
      <div className="mb-6">
        <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-6 mb-6">
          <h4 className="text-xl font-bold text-center mb-4 text-gray-800">
            {content.question}
          </h4>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          {content.options.map((option: string, index: number) => (
            <Button
              key={index}
              variant="ghost"
              className={`bg-white border-2 border-gray-200 hover:border-primary hover:bg-primary hover:text-white rounded-xl py-6 px-4 font-medium text-lg transition-all duration-200 min-h-[80px] ${
                selectedAnswer === option && isCorrect === false
                  ? "border-red-300 bg-red-50 text-red-600"
                  : selectedAnswer === option && isCorrect === true
                  ? "border-green-300 bg-green-50 text-green-600"
                  : ""
              }`}
              onClick={() => checkAnswer(option)}
              disabled={showFeedback}
            >
              <div className="flex items-center justify-center text-center">
                <span className="bg-primary text-white rounded-full w-8 h-8 flex items-center justify-center mr-3 text-sm font-bold">
                  {String.fromCharCode(65 + index)}
                </span>
                {option}
              </div>
            </Button>
          ))}
        </div>
      </div>
    );
  };

  const renderFillInBlankExercise = () => {
    if (!content.question) return null;

    const handleSubmit = () => {
      if (fillInAnswer.trim()) {
        checkAnswer(fillInAnswer.trim());
      }
    };

    const handleKeyDown = (e: React.KeyboardEvent) => {
      if (e.key === 'Enter') {
        handleSubmit();
      }
    };

    return (
      <div className="mb-6">
        <div className="bg-gradient-to-r from-green-50 to-blue-50 rounded-xl p-6 mb-6">
          <h4 className="text-xl font-bold text-center mb-4 text-gray-800">
            {content.question}
          </h4>
        </div>

        <div className="flex flex-col items-center gap-4">
          <Input
            type="text"
            value={fillInAnswer}
            onChange={(e) => setFillInAnswer(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder="Type your answer here..."
            className="text-center text-xl font-medium py-4 px-6 rounded-xl border-2 border-gray-200 focus:border-primary max-w-md"
            disabled={showFeedback}
          />

          <Button
            onClick={handleSubmit}
            disabled={!fillInAnswer.trim() || showFeedback}
            className="bg-primary text-white font-semibold py-3 px-8 rounded-xl hover:bg-opacity-90 transition-colors"
          >
            Submit Answer
          </Button>
        </div>
      </div>
    );
  };

  const renderMatchingExercise = () => {
    if (!content.pairs || !Array.isArray(content.pairs)) return null;

    const handleTermClick = (term: string) => {
      setSelectedTerm(selectedTerm === term ? null : term);
    };

    const handleDefinitionClick = (definition: string) => {
      if (selectedTerm) {
        const newPairs = { ...matchingPairs, [selectedTerm]: definition };
        setMatchingPairs(newPairs);
        setSelectedTerm(null);

        // Check if all pairs are matched
        if (Object.keys(newPairs).length === content.pairs.length) {
          checkAnswer(newPairs);
        }
      }
    };

    const isTermMatched = (term: string) => Boolean(matchingPairs[term]);
    const isDefinitionMatched = (definition: string) => Object.values(matchingPairs).includes(definition);

    return (
      <div className="mb-6">
        <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-xl p-6 mb-6">
          <h4 className="text-xl font-bold text-center mb-4 text-gray-800">
            {content.question || "Match the items on the left with the correct answers on the right"}
          </h4>
          <p className="text-center text-gray-600">Click a term, then click its matching definition!</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* Terms Column */}
          <div className="space-y-3">
            <h5 className="font-semibold text-lg text-center mb-4 text-gray-700">Terms</h5>
            {content.pairs.map((pair: any, index: number) => (
              <Button
                key={`term-${index}`}
                variant="ghost"
                className={`w-full p-4 rounded-xl border-2 transition-all duration-200 ${
                  selectedTerm === pair.term
                    ? "border-primary bg-primary text-white"
                    : isTermMatched(pair.term)
                    ? "border-green-300 bg-green-50 text-green-700"
                    : "border-gray-200 bg-white hover:border-primary"
                }`}
                onClick={() => handleTermClick(pair.term)}
                disabled={isTermMatched(pair.term) || showFeedback}
              >
                {pair.term}
              </Button>
            ))}
          </div>

          {/* Definitions Column */}
          <div className="space-y-3">
            <h5 className="font-semibold text-lg text-center mb-4 text-gray-700">Definitions</h5>
            {content.pairs.map((pair: any, index: number) => (
              <Button
                key={`def-${index}`}
                variant="ghost"
                className={`w-full p-4 rounded-xl border-2 transition-all duration-200 ${
                  isDefinitionMatched(pair.definition)
                    ? "border-green-300 bg-green-50 text-green-700"
                    : selectedTerm
                    ? "border-gray-200 bg-white hover:border-primary"
                    : "border-gray-200 bg-gray-50"
                }`}
                onClick={() => handleDefinitionClick(pair.definition)}
                disabled={isDefinitionMatched(pair.definition) || !selectedTerm || showFeedback}
              >
                {pair.definition}
              </Button>
            ))}
          </div>
        </div>
      </div>
    );
  };

  return (
    <>
      {showFeedback && isCorrect ? (
        <SuccessFeedback
          message={`That's right! Great job solving this ${difficulty} problem.`}
          detail={
            type === 'addition'
              ? `${content.numbers[0]} + ${content.numbers[1]} = ${content.correctAnswer}`
              : type === 'subtraction'
              ? `${content.numbers[0]} - ${content.numbers[1]} = ${content.correctAnswer}`
              : type === 'multiplication'
              ? `${content.numbers[0]} × ${content.numbers[1]} = ${content.correctAnswer}`
              : type === 'division'
              ? `${content.numbers[0]} ÷ ${content.numbers[1]} = ${content.correctAnswer}`
              : type === 'multiple-choice'
              ? `The correct answer is: ${content.correctAnswer}`
              : type === 'fill-in-blank'
              ? `The correct answer is: ${content.answer || content.correctAnswer}`
              : type === 'matching'
              ? "Perfect matching! You got all the pairs right!"
              : "You got it right!"
          }
          onNext={() => {
            setShowFeedback(false);
            setSelectedAnswer(null);
            setIsCorrect(null);
            setFillInAnswer("");
            setMatchingPairs({});
            setSelectedTerm(null);
          }}
        />
      ) : (
        <section className="bg-white rounded-2xl p-6 shadow-sm mb-8">
          <div className="flex justify-between items-center mb-6">
            <h3 className="font-baloo font-semibold text-xl">Exercise {exerciseNumber} of {totalExercises}</h3>
            <div className={difficultyBgColor + " px-3 py-1 rounded-full"}>
              <span className={difficultyColor + " font-medium capitalize"}>{difficulty}</span>
            </div>
          </div>

          <div className="mb-6">
            <h4 className="text-lg font-semibold mb-4">{title}</h4>
            <p className="mb-4 text-text-light">{description}</p>

            {/* Render different exercise types */}
            {type === 'multiple-choice' && renderMultipleChoiceExercise()}
            {type === 'fill-in-blank' && renderFillInBlankExercise()}
            {type === 'matching' && renderMatchingExercise()}
            {(type === 'addition' || type === 'subtraction' || type === 'multiplication' || type === 'division') && (
              <>
                {renderArithmeticExercise()}
                <div>
                  <h4 className="font-semibold mb-3">Select your answer:</h4>
                  <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
                    {content.options && content.options.map((option: any, index: number) => (
                      <Button
                        key={index}
                        variant="ghost"
                        className={`bg-gray-100 hover:bg-primary hover:text-white rounded-xl py-3 font-baloo font-bold text-xl transition-colors ${
                          selectedAnswer === option && isCorrect === false
                            ? "bg-secondary bg-opacity-20"
                            : ""
                        }`}
                        onClick={() => checkAnswer(option)}
                      >
                        {option}
                      </Button>
                    ))}
                  </div>
                </div>
              </>
            )}
            {(type === 'shapes' || type === 'patterns' || type === 'measurement') && (
              <>
                {renderGeometryExercise()}
                <div>
                  <h4 className="font-semibold mb-3">Select your answer:</h4>
                  <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
                    {content.options && content.options.map((option: any, index: number) => (
                      <Button
                        key={index}
                        variant="ghost"
                        className={`bg-gray-100 hover:bg-primary hover:text-white rounded-xl py-3 font-baloo font-bold text-xl transition-colors ${
                          selectedAnswer === option && isCorrect === false
                            ? "bg-secondary bg-opacity-20"
                            : ""
                        }`}
                        onClick={() => checkAnswer(option)}
                      >
                        {option}
                      </Button>
                    ))}
                  </div>
                </div>
              </>
            )}
          </div>
        </section>
      )}
    </>
  );
}
