import { useState, useContext } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import SuccessFeedback from "./SuccessFeedback";
import { useMutation } from "@tanstack/react-query";
import { UserContext } from "../App";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";

interface ExerciseCardProps {
  exerciseNumber: number;
  totalExercises: number;
  title: string;
  description: string;
  difficulty: string;
  content: any;
  type: string;
  exerciseId: number;
  onComplete: (score: number) => void;
}

export default function ExerciseCard({
  exerciseNumber,
  totalExercises,
  title,
  description,
  difficulty,
  content,
  type,
  exerciseId,
  onComplete,
}: ExerciseCardProps) {
  const { user } = useContext(UserContext);
  const { toast } = useToast();
  const [selectedAnswer, setSelectedAnswer] = useState<number | string | null>(null);
  const [isCorrect, setIsCorrect] = useState<boolean | null>(null);
  const [showFeedback, setShowFeedback] = useState(false);
  const [attempts, setAttempts] = useState(0);
  
  // Mutation for submitting student answers
  const submitAnswerMutation = useMutation({
    mutationFn: async (data: { 
      userId: string; 
      exerciseId: number; 
      answer: string | number;
    }) => {
      return apiRequest<{isCorrect: boolean, studentAnswer: any}>("/api/student-answers", {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify(data)
      });
    },
    onSuccess: (data) => {
      // Use the server's validation of correctness
      setIsCorrect(data.isCorrect);
      setShowFeedback(true);
      
      if (data.isCorrect) {
        // Calculate score based on difficulty and attempts
        const scoreMap = { easy: 5, medium: 10, hard: 15 };
        const baseScore = scoreMap[difficulty as keyof typeof scoreMap] || 5;
        // Reduce score for multiple attempts
        const attemptsDeduction = Math.max(0, attempts - 1) * 2;
        const finalScore = Math.max(1, baseScore - attemptsDeduction);
        
        // Delay completing the exercise to show feedback
        setTimeout(() => {
          onComplete(finalScore);
        }, 2000);
      } else {
        // If incorrect, allow another attempt
        toast({
          title: "Not quite right",
          description: "Try again! Learning happens through practice.",
          variant: "destructive",
        });
      }
    },
    onError: (error) => {
      console.error("Error submitting answer:", error);
      toast({
        title: "Error submitting answer",
        description: "There was a problem saving your answer. Please try again.",
        variant: "destructive",
      });
    }
  });

  const difficultyColor = {
    easy: "text-accent",
    medium: "text-primary",
    hard: "text-secondary"
  }[difficulty] || "text-accent";

  const difficultyBgColor = {
    easy: "bg-accent bg-opacity-10",
    medium: "bg-primary bg-opacity-10",
    hard: "bg-secondary bg-opacity-10"
  }[difficulty] || "bg-accent bg-opacity-10";

  const checkAnswer = (answer: number | string) => {
    setSelectedAnswer(answer);
    setAttempts(prev => prev + 1);
    
    // Submit the answer to the server for validation
    if (user) {
      submitAnswerMutation.mutate({
        userId: user.id,
        exerciseId,
        answer
      });
    } else {
      // Fallback local validation if user context is not available
      const correct = 
        type === 'addition' || type === 'subtraction' || type === 'multiplication' || type === 'division'
          ? answer === content.correctAnswer
          : answer === content.correctAnswer;
      
      setIsCorrect(correct);
      setShowFeedback(true);
      
      if (correct) {
        // Calculate score based on difficulty
        const scoreMap = { easy: 5, medium: 10, hard: 15 };
        const score = scoreMap[difficulty as keyof typeof scoreMap] || 5;
        
        // Delay completing the exercise to show feedback
        setTimeout(() => {
          onComplete(score);
        }, 2000);
      }
    }
  };

  const renderArithmeticExercise = () => {
    if (!content.numbers) return null;
    
    return (
      <div className="flex justify-center items-center gap-8 mb-6">
        {content.numbers.map((num: number, index: number) => (
          <div key={index} className="flex flex-col items-center">
            {type === 'addition' && (
              <div className="grid grid-cols-2 gap-2 mb-4">
                {Array.from({ length: num }).map((_, i) => (
                  <svg 
                    key={i} 
                    className="w-16 h-16" 
                    viewBox="0 0 100 100" 
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <circle cx="50" cy="50" r="45" fill="#ff6b6b" />
                    <circle cx="32" cy="40" r="6" fill="white" />
                    <circle cx="68" cy="40" r="6" fill="white" />
                    <path d="M 30 65 Q 50 80 70 65" stroke="white" strokeWidth="4" fill="none" />
                  </svg>
                ))}
              </div>
            )}
            <span className="text-lg font-semibold">{num}</span>
          </div>
        ))}
        
        <div className="text-2xl font-bold">
          {type === 'addition' && '+'}
          {type === 'subtraction' && '-'}
          {type === 'multiplication' && '×'}
          {type === 'division' && '÷'}
        </div>
        
        <div className="flex flex-col items-center">
          <div className="h-24 w-24 rounded-xl border-2 border-dashed border-gray-300 flex items-center justify-center">
            <span className="text-2xl font-bold text-gray-300">?</span>
          </div>
        </div>
      </div>
    );
  };

  const renderGeometryExercise = () => {
    if (!content.shapes) return null;
    
    return (
      <div className="flex justify-center items-center gap-8 mb-6">
        {content.shapes.map((shape: any, index: number) => (
          <div key={index} className="flex flex-col items-center">
            <div className="w-24 h-24 flex items-center justify-center mb-2">
              {shape.name === 'circle' && (
                <div className={`w-20 h-20 rounded-full bg-${shape.color === 'red' ? 'secondary' : shape.color === 'blue' ? 'primary' : 'accent'}`}></div>
              )}
              {shape.name === 'square' && (
                <div className={`w-20 h-20 bg-${shape.color === 'red' ? 'secondary' : shape.color === 'blue' ? 'primary' : 'accent'}`}></div>
              )}
              {shape.name === 'triangle' && (
                <div 
                  className={`w-20 h-20 bg-${shape.color === 'red' ? 'secondary' : shape.color === 'blue' ? 'primary' : 'accent'}`}
                  style={{ clipPath: 'polygon(50% 0%, 0% 100%, 100% 100%)' }}
                ></div>
              )}
              {shape.name === 'rectangle' && (
                <div className={`w-24 h-16 bg-${shape.color === 'red' ? 'secondary' : shape.color === 'blue' ? 'primary' : 'accent'}`}></div>
              )}
            </div>
            <span className="text-lg font-semibold capitalize">{shape.name}</span>
          </div>
        ))}
      </div>
    );
  };

  return (
    <>
      {showFeedback && isCorrect ? (
        <SuccessFeedback
          message={`That's right! Great job solving this ${difficulty} problem.`}
          detail={
            type === 'addition' 
              ? `${content.numbers[0]} + ${content.numbers[1]} = ${content.correctAnswer}`
              : "You got it right!"
          }
          onNext={() => {
            setShowFeedback(false);
            setSelectedAnswer(null);
            setIsCorrect(null);
          }}
        />
      ) : (
        <section className="bg-white rounded-2xl p-6 shadow-sm mb-8">
          <div className="flex justify-between items-center mb-6">
            <h3 className="font-baloo font-semibold text-xl">Exercise {exerciseNumber} of {totalExercises}</h3>
            <div className={difficultyBgColor + " px-3 py-1 rounded-full"}>
              <span className={difficultyColor + " font-medium capitalize"}>{difficulty}</span>
            </div>
          </div>
          
          <div className="mb-6">
            <h4 className="text-lg font-semibold mb-4">{title}</h4>
            <p className="mb-4 text-text-light">{description}</p>
            
            {type === 'addition' || type === 'subtraction' || type === 'multiplication' || type === 'division'
              ? renderArithmeticExercise()
              : renderGeometryExercise()}
          </div>
          
          <div>
            <h4 className="font-semibold mb-3">Select your answer:</h4>
            <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
              {content.options && content.options.map((option: any, index: number) => (
                <Button
                  key={index}
                  variant="ghost"
                  className={`bg-gray-100 hover:bg-primary hover:text-white rounded-xl py-3 font-baloo font-bold text-xl transition-colors ${
                    selectedAnswer === option && isCorrect === false
                      ? "bg-secondary bg-opacity-20"
                      : ""
                  }`}
                  onClick={() => checkAnswer(option)}
                >
                  {option}
                </Button>
              ))}
            </div>
          </div>
        </section>
      )}
    </>
  );
}
