import { useState, useEffect } from "react";
import { User } from "@shared/schema";
import { supabase } from "@/lib/supabase";
import type { User as SupabaseUser } from '@supabase/supabase-js';

export function useAuth() {
  const [supabaseUser, setSupabaseUser] = useState<SupabaseUser | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Get initial session
    const getInitialSession = async () => {
      try {
        setIsLoading(true);
        
        const { data } = await supabase.auth.getSession();
        
        if (data?.session?.user) {
          setSupabaseUser(data.session.user);
          
          // Convert Supabase user to our app User type
          // Convert Google user data to our app User format
          const userData = {
            id: data.session.user.id,
            email: data.session.user.email || '',
            firstName: data.session.user.user_metadata?.name?.split(' ')[0] || null,
            lastName: data.session.user.user_metadata?.name?.split(' ').slice(1).join(' ') || null,
            profileImageUrl: data.session.user.user_metadata?.avatar_url || null,
            age: null,
            parentEmail: null,
            createdAt: new Date(),
            updatedAt: new Date()
          };
          
          console.log('User logged in:', userData);
          setUser(userData);
        } else {
          setSupabaseUser(null);
          setUser(null);
        }
      } catch (error) {
        console.error('Error getting auth session:', error);
        setSupabaseUser(null);
        setUser(null);
      } finally {
        setIsLoading(false);
      }
    };
    
    getInitialSession();
    
    // Set up auth listener for changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        setSupabaseUser(session?.user || null);
        
        if (session?.user) {
          // Create user data object from Google auth response
          const userData = {
            id: session.user.id,
            email: session.user.email || '',
            firstName: session.user.user_metadata?.name?.split(' ')[0] || null,
            lastName: session.user.user_metadata?.name?.split(' ').slice(1).join(' ') || null,
            profileImageUrl: session.user.user_metadata?.avatar_url || null,
            age: null,
            parentEmail: null,
            createdAt: new Date(),
            updatedAt: new Date()
          };
          
          console.log('Auth state changed:', userData);
          setUser(userData);
        } else {
          setUser(null);
        }
      }
    );
    
    return () => {
      subscription.unsubscribe();
    };
  }, []);

  return {
    user,
    isLoading,
    isAuthenticated: !!supabaseUser,
  };
}